<script>
import _ from 'lodash'
import { FilterConfig as FILTER_CONFIG } from 'wolf-static-cpnt/event/config'
import { InputNumberWithSelect } from '@/components/utils'

const eventFirstValues = _.keys(FILTER_CONFIG.FIRST_ACTION)
const eventFirstNames = _.values(FILTER_CONFIG.FIRST_ACTION)
const timeUnitValues = _.keys(FILTER_CONFIG.TIME_TYPE)
const timeUnitNames = _.values(FILTER_CONFIG.TIME_TYPE)

export default {
  name: 'FilterTimeInput',
  components: {
    InputNumberWithSelect,
  },
  inject: {
    filterListGroup: {
      from: 'filterListGroup',
      default: () => null,
    },
    filterContext: {
      from: 'filterContext',
      default: () => null,
    },
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    type: {
      type: String,
      default: 'first', // 'first' | 'last'
      validator: value => ['first', 'last'].includes(value),
    },
  },
  data() {
    return {
      eventFirstValues,
      eventFirstNames,
      timeUnitValues,
      timeUnitNames,
    }
  },
  computed: {
    // 根据type选择对应的字段
    timeValue() {
      return this.type === 'first' ? this.value.firstTimeValue : this.value.lastTimeValue
    },
    timeUnit() {
      return this.type === 'first' ? this.value.firstTimeUnit : this.value.lastTimeUnit
    },
    // 判断是否为"先做过, 后未做过"
    isFirstDoLastNotDo() {
      return this.value.isFirstDoLastNotDo()
    },
    // 显示文本配置
    displayConfig() {
      if (this.type === 'first') {
        return {
          prefix: '在',
          suffix: '之内',
          showActionSelect: true,
        }
      }
      else {
        return {
          prefix: '并在接下来的',
          suffix: '之内',
          showActionSelect: false,
        }
      }
    },
    // 时间单位选项（格式化为 InputNumberWithSelect 需要的格式）
    timeUnitOptions() {
      return timeUnitValues.map((value, index) => ({
        value,
        label: timeUnitNames[index],
      }))
    },
    // 输入框宽度
    inputWidth() {
      return 80
    },
    // 选择框宽度
    selectWidth() {
      return 80
    },
    // 判断是否应该禁用firstAction选择框
    shouldDisableFirstActionSelect() {
      if (this.type !== 'first')
        return false

      const context = this.filterContext ? this.filterContext() : null
      const externalFirstAction = context?.externalFirstAction

      if (externalFirstAction) {
        return true
      }

      // 获取完整的过滤组数据
      const filterListGroup = this.filterListGroup ? this.filterListGroup() : null
      if (!filterListGroup || !filterListGroup.filters)
        return false

      // 计算过滤器总数
      const filterCount = filterListGroup.filters
        .map(v => v.filters?.length || 0)
        .reduce((a, b) => a + b, 0)

      // 如果过滤器数量大于1，则禁用
      return filterCount > 1
    },
  },
  methods: {
    handleEventFirstChange(v) {
      if (this.type !== 'first')
        return

      let action
      if (v === 'DONE') {
        action = 'DONE'
      }
      else if (v === 'FIRST_DO_LAST_NOT_DO') {
        action = 'FIRST_DO'
      }
      else if (v === 'DO_SEQ') {
        action = 'DO_SEQ'
      }
      this.value.clearProperty()

      this.value.changeProperty({
        ...this.value,
        firstAction: v,
        action,
        lastAction: v === 'FIRST_DO_LAST_NOT_DO' ? 'NOT_DO' : undefined,
      })
      this.onChange(this.value)
    },

    handleTimeValueChange(v) {
      const updateData = { ...this.value }
      if (this.type === 'first') {
        updateData.firstTimeValue = v
      }
      else {
        updateData.lastTimeValue = v
      }

      this.value.changeProperty(updateData)
      this.onChange(this.value)
    },

    handleTimeUnitChange(v) {
      const updateData = { ...this.value }
      if (this.type === 'first') {
        updateData.firstTimeUnit = v
      }
      else {
        updateData.lastTimeUnit = v
      }

      this.value.changeProperty(updateData)
      this.onChange(this.value)
    },

    // 处理 InputNumberWithSelect 组件的时间值变更
    handleTimeInputChange(value) {
      this.handleTimeValueChange(value)
    },

    // 处理 InputNumberWithSelect 组件的单位变更
    handleTimeSelectChange(value) {
      this.handleTimeUnitChange(value)
    },
  },
}
</script>

<template>
  <span style="display: flex; align-items: center; gap: 8px">
    <!-- 行为选择框 (仅在first类型且非FIRST_DO_LAST_NOT_DO模式下显示) -->
    <a-select
      v-if="displayConfig.showActionSelect"
      :disabled="shouldDisableFirstActionSelect"
      :value="value.firstAction"
      style="width: 150px"
      placeholder="行为"
      @change="handleEventFirstChange"
    >
      <a-select-option v-for="(item, index) in eventFirstValues" :key="item" :value="item">
        {{ eventFirstNames[index] }}
      </a-select-option>
    </a-select>

    <!-- 时间输入区域 -->
    <span
      v-if="!(type === 'first' && isFirstDoLastNotDo)"
      :style="{ display: 'flex', alignItems: 'center', gap: '8px' }"
    >
      <span>{{ displayConfig.prefix }}</span>

      <InputNumberWithSelect
        :value="timeValue"
        :select-value="timeUnit"
        :select-options="timeUnitOptions"
        :min="1"
        :max="999"
        :input-width="inputWidth"
        :select-width="selectWidth"
        placeholder="时间"
        select-placeholder="时间单位"
        :on-number-change="handleTimeInputChange"
        :on-select-change="handleTimeSelectChange"
      />

      <span>{{ displayConfig.suffix }}</span>
    </span>
  </span>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
