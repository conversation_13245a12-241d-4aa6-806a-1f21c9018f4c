<script>
import { Button } from 'ant-design-vue'
import _ from 'lodash'
import EventFilter from '../event/components/filter/Filter.vue'
import CPNTFilter from '../filter/Filter.vue'
import Label from '../label/Filter.vue'
import Segment from '../segment/Filter.vue'
import ActionCollectiveGroup from './ActionCollectiveGroup.vue'
import HandleGroup from './handlegroup.vue'

export default {
  name: 'ActionCollective',
  components: {
    'a-button': Button,
    ActionCollectiveGroup,
    CPNTFilter,
    Label,
    EventFilter,
    HandleGroup,
    Segment,
  },
  provide() {
    return {
      actionCollectiveContext: {
        setPushDataExclusiveAcrossComponents: this.setPushDataExclusiveAcrossComponents,
      },
    }
  },
  props: {
    dataProvider: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    showInitLine: {
      type: Boolean,
      default: false,
    },
    isActionCollection: {
      type: Boolean,
      default: false,
    },
    isUserGroup: {
      type: Boolean,
      default: false,
    },
    showPushData: {
      type: Boolean,
      default: false,
    },
    externalFirstAction: {
      type: [String, Object],
      default: null,
    },
  },
  data() {
    return {
      refObj: {},
      _,
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (_.isEmpty(newValue)) {
          if (this.showInitLine) {
            this.onChange({
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  eventGroup: {},
                },
              ],
            })
          }
          else {
            this.onChange({
              connector: 'AND',
              filters: [],
            })
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 设置ref引用
    setRef(index, key, el) {
      if (!this.refObj[index]) {
        this.refObj[index] = {}
      }
      this.refObj[index][key] = el
    },

    // 验证方法
    isValid() {
      const resultArr = []
      _.forEach(this.refObj, (v) => {
        const values = _.values(v)
        _.forEach(_.without(values, null), (item) => {
          if (item && item.isValid) {
            resultArr.push(item.isValid(true))
          }
        })
      })
      return !resultArr.includes(false)
    },

    // 跨组件设置 pushData 互斥
    setPushDataExclusiveAcrossComponents(targetFilter, pushDataValue) {
      console.log('ActionCollective setPushDataExclusiveAcrossComponents 被调用', {
        targetFilterKey: targetFilter?.key,
        pushDataValue,
        hasValue: !!this.value,
        hasFilters: !!(this.value && this.value.filters),
        filtersLength: this.value?.filters?.length || 0,
      })

      if (!this.value || !this.value.filters) {
        console.log('没有 value 或 filters，直接返回')
        return
      }

      const targetFilterKey = targetFilter?.key

      // 遍历所有组件的所有过滤器
      this.value.filters.forEach((item, itemIndex) => {
        // 处理 eventGroup
        if (item.eventGroup && item.eventGroup.filters) {
          console.log(`处理第 ${itemIndex} 个 item 的 eventGroup，有 ${item.eventGroup.filters.length} 个 filterGroup`)
          item.eventGroup.filters.forEach((filterGroup, groupIndex) => {
            if (filterGroup && filterGroup.filters) {
              console.log(`  处理第 ${groupIndex} 个 filterGroup，有 ${filterGroup.filters.length} 个 subFilter`)
              filterGroup.filters.forEach((subFilter, subIndex) => {
                // 检查 subFilter 是否还有嵌套的 filters
                if (subFilter && subFilter.filters && Array.isArray(subFilter.filters)) {
                  console.log(`    处理第 ${subIndex} 个 subFilter，有 ${subFilter.filters.length} 个 filter`)
                  subFilter.filters.forEach((filter, filterIndex) => {
                    console.log(`      处理第 ${filterIndex} 个 filter，key: ${filter.key}, pushData: ${filter.pushData}`)
                    if (filter.key === targetFilterKey) {
                      console.log(`        这是目标 filter，设置 pushData 为 ${pushDataValue}`)
                      filter.changePushData(pushDataValue)
                      console.log(`        设置后 pushData: ${filter.pushData}`)
                    }
                    else if (pushDataValue || targetFilter === null) {
                      console.log(`        这不是目标 filter，设置 pushData 为 false`)
                      filter.changePushData(false)
                      console.log(`        设置后 pushData: ${filter.pushData}`)

                      // 同时清除该 filter 的 todayDoEvents 的 pushData
                      if (filter.todayDoEvents && Array.isArray(filter.todayDoEvents)) {
                        filter.todayDoEvents.forEach((event, i) => {
                          filter.updateTodayDoEvent(i, { pushData: false })
                        })
                      }
                    }
                  })
                }
                else if (subFilter && subFilter.key) {
                  // 如果 subFilter 本身就是一个 filter
                  console.log(`    处理第 ${subIndex} 个 filter，key: ${subFilter.key}, pushData: ${subFilter.pushData}`)
                  if (subFilter.key === targetFilterKey) {
                    console.log(`      这是目标 filter，设置 pushData 为 ${pushDataValue}`)
                    subFilter.changePushData(pushDataValue)
                    console.log(`      设置后 pushData: ${subFilter.pushData}`)
                  }
                  else if (pushDataValue || targetFilter === null) {
                    console.log(`      这不是目标 filter，设置 pushData 为 false`)
                    subFilter.changePushData(false)
                    console.log(`      设置后 pushData: ${subFilter.pushData}`)

                    // 同时清除该 filter 的 todayDoEvents 的 pushData
                    if (subFilter.todayDoEvents && Array.isArray(subFilter.todayDoEvents)) {
                      subFilter.todayDoEvents.forEach((event, i) => {
                        subFilter.updateTodayDoEvent(i, { pushData: false })
                      })
                    }
                  }
                }
              })
            }
          })
        }

        // 处理 userLabel
        if (item.userLabel && item.userLabel.filters) {
          item.userLabel.filters.forEach((filterGroup) => {
            if (filterGroup && filterGroup.filters) {
              filterGroup.filters.forEach((filter) => {
                if (filter.key === targetFilterKey) {
                  filter.changePushData(pushDataValue)
                }
                else if (pushDataValue || targetFilter === null) {
                  filter.changePushData(false)
                }
              })
            }
          })
        }

        // 处理 segment
        if (item.segment && item.segment.filters) {
          item.segment.filters.forEach((filterGroup) => {
            if (filterGroup && filterGroup.filters) {
              filterGroup.filters.forEach((filter) => {
                if (filter.key === targetFilterKey) {
                  filter.changePushData(pushDataValue)
                }
                else if (pushDataValue || targetFilter === null) {
                  filter.changePushData(false)
                }
              })
            }
          })
        }

        // 处理 userProperty（如果存在）
        if (item.userProperty && item.userProperty.filters) {
          item.userProperty.filters.forEach((filterGroup) => {
            if (filterGroup && filterGroup.filters) {
              filterGroup.filters.forEach((filter) => {
                if (filter.key === targetFilterKey) {
                  filter.changePushData(pushDataValue)
                }
                else if (pushDataValue || targetFilter === null) {
                  filter.changePushData(false)
                }
              })
            }
          })
        }
      })

      // 触发数据更新
      this.onChange({ ...this.value })
    },

    onChangeConnector(datas, type) {
      const result = { ...this.value, [type]: datas }
      this.onChange(result)
    },

    onSubConnectorChange(datas, type, data) {
      data[type] = datas
      this.onChange({ ...this.value })
    },

    count() {
      return (this.value && this.value.filters && this.value.filters.length) || 1
    },

    handleAddSubItem(type, index) {
      const _value = _.cloneDeep(this.value)
      _value.filters[index][type] = {}
      this.onChange(_value)
    },

    onAddItemFilter(type) {
      const _value = _.cloneDeep(this.value)
      _value.filters.push({
        connector: 'AND',
        [type]: {},
      })
      this.onChange(_value)
    },

    onChangeItemFilter(type, v, index, innerValue) {
      const _value = _.cloneDeep(this.value)
      if (_.isEmpty(innerValue.filters)) {
        _value.filters[index] = _.omit(_value.filters[index], type)
        if (
          !_value.filters[index].eventGroup
          && !_value.filters[index].userProperty
          && !_value.filters[index].userLabel
          && !_value.filters[index].segment
        ) {
          _value.filters.splice(index, 1)
        }
      }
      else {
        _value.filters[index][type] = innerValue
      }
      // 传递两个参数：validJson 和 fullValue
      this.onChange && this.onChange(_value, _value)
    },

    getItemKeys(item) {
      return _.keys(item).slice(1)
    },

    getOrderedKeys(item) {
      const keys = this.getItemKeys(item)
      // 整理顺序
      const _keys = []
      _keys[0] = _.find(keys, key => key === 'eventGroup')
      _keys[1] = _.find(keys, key => key === 'userProperty')
      _keys[2] = _.find(keys, key => key === 'userLabel')
      _keys[3] = _.find(keys, key => key === 'segment')

      return _.without(_keys, false, undefined, null)
    },
  },
}
</script>

<template>
  <div class="wolf-static-component_filter_FilterGroupPanel_action_collective">
    <ActionCollectiveGroup
      v-if="!(!showInitLine && _.isEmpty(value?.filters))"
      :connector="value.connector"
      :on-change-connector="(res) => onChangeConnector(res, 'connector')"
      :filter-count="count()"
      inner="inner"
      :mode="mode"
    >
      <template v-for="(item, index) in value.filters">
        <ActionCollectiveGroup
          :key="index"
          :connector="item.connector"
          :on-change-connector="(res) => onSubConnectorChange(res, 'connector', item)"
          :filter-count="getItemKeys(item).length"
          inner="inner"
          :mode="mode"
        >
          <div>
            <template v-for="key in getOrderedKeys(item)">
              <!-- 事件组 -->
              <EventFilter
                v-if="key === 'eventGroup'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :show-init-line="true"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('eventGroup', v, index, innerValue)"
                :mode="mode"
                :is-action-collection="isActionCollection"
                :show-push-data="showPushData"
                :external-first-action="externalFirstAction"
                type="actioncollective"
              />

              <!-- 用户属性 -->
              <CPNTFilter
                v-else-if="key === 'userProperty'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                add-button-text="属性"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('userProperty', v, index, innerValue)"
                :mode="mode"
                :is-user-group="isUserGroup"
              />

              <!-- 用户标签 -->
              <Label
                v-else-if="key === 'userLabel'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('userLabel', v, index, innerValue)"
                :mode="mode"
                :is-user-group="isUserGroup"
              />

              <!-- 分群 -->
              <Segment
                v-else-if="key === 'segment'"
                :ref="(el) => setRef(index, key, el)"
                :key="key"
                :data-provider="dataProvider"
                :value="item[key] || {}"
                :on-change="(v, innerValue) => onChangeItemFilter('segment', v, index, innerValue)"
                :mode="mode"
              />
            </template>

            <HandleGroup :value="item" :index="index" :on-click="(type) => handleAddSubItem(type, index)" :mode="mode" />
          </div>
        </ActionCollectiveGroup>
      </template>
    </ActionCollectiveGroup>

    <div class="action_collective_button_group" :style="{ display: mode === 'detail' ? 'none' : 'block' }">
      <a-button type="dashed" @click="() => onAddItemFilter('eventGroup')">
        <a-icon type="plus" />
        实时行为
      </a-button>
      <!-- <a-button type="dashed" @click="() => onAddItemFilter('userProperty')">
        <a-icon type="plus" />
        属性
      </a-button> -->
      <a-button type="dashed" @click="() => onAddItemFilter('userLabel')">
        <a-icon type="plus" />
        标签
      </a-button>
      <a-button type="dashed" @click="() => onAddItemFilter('segment')">
        <a-icon type="plus" />
        AI决策模型
      </a-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
