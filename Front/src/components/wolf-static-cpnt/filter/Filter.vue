<script>
import _ from 'lodash'
import Log from '../../utils/log'
import FilterConfig from './FilterConfig'
import { FilterContextProvider } from './FilterContext'
import FilterListGroup from './FilterListGroup.vue'
import FilterModelUtil from './FilterModelUtil'

const log = Log.getLogger('Filter')

export default {
  name: 'CPNTFilter',
  components: {
    FilterListGroup,
    FilterContextProvider,
  },
  props: {
    dataProvider: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
    hideAdd: {
      type: Boolean,
      default: false,
    },
    hideInit: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: '',
    },
    addButtonText: {
      type: String,
      default: '添加过滤',
    },
    isUserGroup: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const { maxFilterCount } = FilterConfig
    return {
      maxFilterCount,
      currentValue: this.initValue(),
      propsValue: {},
      validating: false, // 添加validating状态
    }
  },
  computed: {
    context() {
      let filterCount = 0
      if (this.currentValue && this.currentValue.filters) {
        filterCount = this.currentValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)
      }

      return {
        dataProvider: this.dataProvider,
        logProvider: Log,
        canAdd: filterCount < this.maxFilterCount,
        mode: this.mode || 'edit',
        filterCount,
        validating: this.validating, // 使用data中的validating状态
        hideAdd: this.hideAdd,
        hideInit: this.hideInit,
        isUserGroup: this.isUserGroup,
      }
    },
    shouldShow() {
      return !(this.hideInit && this.hideAdd && _.isEmpty(this.currentValue || this.currentValue.filters))
    },
  },

  watch: {
    value: {
      handler(newValue) {
        log.debug('props.value changed', JSON.stringify(newValue))
        if ((!_.isEmpty(newValue) || !_.isEmpty(this.propsValue)) && !_.isEqual(newValue, this.propsValue)) {
          this.currentValue = FilterModelUtil.fromJson(newValue)
        }
      },
      deep: true,
    },
    mode: {
      handler(newVal) {
        log.debug('mode 变化了:', newVal)
      },
    },
  },
  mounted() {
    log.debug('Before Render', JSON.stringify(this.currentValue), this.context.canAdd)
  },

  methods: {
    initValue() {
      return this.value && this.value.filters && this.value.filters.length > 0
        ? FilterModelUtil.fromJson(this.value)
        : FilterModelUtil.initCreateFilterGroup(this.hideInit)
    },

    /**
     * 添加过滤组
     */
    addFilterGroup() {
      const _value = _.isEmpty(this.currentValue)
        ? {
            connector: 'AND',
            filters: [],
          }
        : this.currentValue

      FilterModelUtil.addFilterGroupWithOneFilter(_value)
      this.onValueChange(_value)
      return _value
    },

    /**
     * 当过滤组修改时回调
     * @param {object} v 过滤组
     */
    onValueChange(v) {
      log.debug('onValueChanged', JSON.stringify(v))
      const _v = FilterModelUtil.getValidJson(v)
      this.propsValue = _v
      this.onChange(_v, v)
      this.currentValue = { ...v }
    },

    /**
     * 验证方法
     */
    isValid(flag) {
      // 设置验证状态
      this.validating = true

      const result = FilterModelUtil.isFilterListGroupValid(this.currentValue, flag)

      // 验证完成后重置状态
      this.$nextTick(() => {
        this.validating = false
      })
      return result
    },

    /**
     * 获取值
     */
    getValue() {
      return this.currentValue
    },

    /**
     * 获取过滤器数量
     */
    getFilterCount() {
      if (!_.isEmpty(this.currentValue.filters)) {
        return this.currentValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)
      }
      return 0
    },
  },
}
</script>

<template>
  <FilterContextProvider :value="context">
    <div
      :class="`wolf-static-component_filter_FilterGroupPanel ${className || ''}`"
      :style="{
        display: shouldShow ? 'block' : 'none',
      }"
    >
      <FilterListGroup :value="currentValue" :on-change="onValueChange" />
      <div v-if="!hideAdd" class="FilterAdder">
        <a-button
          type="dashed"
          :disabled="!context.canAdd"
          :style="{
            display: context.mode === 'detail' ? 'none' : 'inline-block',
          }"
          @click="addFilterGroup"
        >
          + {{ addButtonText }}
        </a-button>
        <span
          :style="{
            marginLeft: '10px',
            display: context.mode === 'detail' ? 'none' : 'inline',
          }"
        >
          [{{ context.filterCount }}/{{ maxFilterCount }}] 最多添加{{ maxFilterCount }}条
        </span>
      </div>
    </div>
  </FilterContextProvider>
</template>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
