<script>
import { FilterModelUtil } from 'wolf-static-cpnt/event'
import { CPNTActionCollective } from '@/components/wolf-static-cpnt/index'
import { demoValue2, mockEventDataProvider } from './TestEventConfig'
import { labelMockDataProvider } from './testLabelConfig'

export default {
  name: 'TestActioncollective',
  components: {
    CPNTActionCollective,
  },
  data() {
    return {
      jsonData: '{}',
      filterValue: {},
      filterValue2: {}, // 第二个ActionCollective组件的值
      mode: 'edit',
      mockDataProvider: { },
    }
  },
  watch: {
    filterValue: {
      handler(newVal) {
        this.jsonData = JSON.stringify(newVal, null, 2)
      },
      deep: true,
    },
  },
  mounted() {
    // 初始化一个空的过滤器
    this.filterValue = demoValue2

    window.setActionCollectiveValue = this.setDemoValue

    this.mockDataProvider = { ...mockEventDataProvider, ...labelMockDataProvider }
  },
  methods: {
    handleFilterChange(validJson, _fullValue) {
      this.filterValue = validJson || {}
    },

    handleFilterChange2(validJson, _fullValue) {
      this.filterValue2 = validJson || {}
    },

    getFirstFilterFirstAction() {
      const first = FilterModelUtil.getActioncollevtiveFirstFilterFirstAction(this.filterValue)
      const last = FilterModelUtil.getActioncollevtiveFirstFilterFirstAction(this.filterValue2)
      return {
        firstAction: first?.firstAction || last?.firstAction,
        totalCount: (first?.totalCount || 0) + (last?.totalCount || 0),
      }
    },

    toggleMode() {
      this.mode = this.mode === 'edit' ? 'detail' : 'edit'
    },

    clearFilter() {
      this.filterValue = {
        connector: 'AND',
        filters: [],
      }
      this.filterValue2 = {
        connector: 'AND',
        filters: [],
      }
    },

    validateFilter() {
      if (this.$refs.actionCollectiveRef) {
        const isValid = this.$refs.actionCollectiveRef.isValid()
        window.console.log('ActionCollective isValid:', isValid)
        this.$message[isValid ? 'success' : 'error'](`第一个过滤器验证结果: ${isValid ? '通过' : '失败'}`)
      }
    },

    validateFilter2() {
      if (this.$refs.actionCollectiveRef2) {
        const isValid = this.$refs.actionCollectiveRef2.isValid()
        window.console.log('ActionCollective2 isValid:', isValid)
        this.$message[isValid ? 'success' : 'error'](`第二个过滤器验证结果: ${isValid ? '通过' : '失败'}`)
      }
    },

    submitJsonData() {
      try {
        this.filterValue = JSON.parse(this.jsonData)
      }
      catch (error) {
        this.$message.error(`JSON格式错误: ${error.message}`)
      }
    },

    setDemoValue() {
      this.filterValue = demoValue2
    },
  },
}
</script>

<template>
  <div class="test-actioncollective-page">
    <h1>ActionCollective Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode">
        切换模式 (当前: {{ mode }})
      </a-button>
      <a-button style="margin-left: 8px" @click="clearFilter">
        清空过滤器
      </a-button>
      <a-button style="margin-left: 8px" @click="validateFilter">
        验证第一个过滤器
      </a-button>
      <a-button style="margin-left: 8px" @click="validateFilter2">
        验证第二个过滤器
      </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section bg-[#fff]">
          <h2>第一个 ActionCollective Filter (主Filter)</h2>
          <p style="color: #666; margin-bottom: 16px;">
            这个Filter会使用第二个Filter的firstAction作为新增过滤器的默认值
            <br>
            当前第二个Filter的firstAction: <strong>{{ JSON.stringify(getFirstFilterFirstAction()) || '无' }}</strong>
          </p>
          <CPNTActionCollective
            ref="actionCollectiveRef"
            :value="filterValue"
            :data-provider="mockDataProvider"
            :on-change="handleFilterChange"
            :mode="mode"
            :show-init-line="true"
            :is-action-collection="true"
            :is-user-group="false"
            :show-push-data="true"
            :external-first-action="getFirstFilterFirstAction('last')"
          />
        </div>

        <div class="test-section bg-[#f9f9f9]">
          <h2>第二个 ActionCollective Filter (联动Filter)</h2>
          <p style="color: #666; margin-bottom: 16px;">
            这个Filter会使用第一个Filter的firstAction作为新增过滤器的默认值
            <br>
            当前第一个Filter的firstAction: <strong>{{ JSON.stringify(getFirstFilterFirstAction()) || '无' }}</strong>
          </p>
          <CPNTActionCollective
            ref="actionCollectiveRef2"
            :value="filterValue2"
            :data-provider="mockDataProvider"
            :on-change="handleFilterChange2"
            :mode="mode"
            :show-init-line="true"
            :is-action-collection="true"
            :is-user-group="false"
            :external-first-action="getFirstFilterFirstAction('first')"
          />
        </div>

        <div class="test-section flex gap-24">
          <div>
            <h3>第一个ActionCollective的值:</h3>
            <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
          </div>

          <div>
            <h3>第二个ActionCollective的值:</h3>
            <pre>{{ JSON.stringify(filterValue2, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button style="margin-bottom: 10px" @click="submitJsonData">
            应用JSON数据
          </a-button>
          <a-button style="margin-bottom: 10px" @click="setDemoValue">
            设置demo数据
          </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-actioncollective-page {
  padding: 20px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.json-editor {
  padding: 10px;
}

.flex {
  display: flex;
  gap: 20px;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[15\%\] {
  width: 15%;
}
</style>
